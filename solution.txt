
1. Create books collection with validation rule:
db.createCollection("books", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["title"],
      properties: {
        title: {
          bsonType: "string",
          description: "must be a non-empty string"
        }
      }
    }
  }
})

2. Insert into authors collection:
db.authors.insertOne({ name: "<PERSON>", birthYear: 1920 })

3. Create capped collection logs:
db.createCollection("logs", { capped: true, size: 1048576 })

4. Create index on title:
db.books.createIndex({ title: 1 })

5. Insert one book:
db.books.insertOne({ title: "I, Robot", author: "<PERSON>imov", year: 1950, genres: ["Science Fiction"] })

6. Insert multiple books:
db.books.insertMany([
  { title: "The Martian", author: "<PERSON>", year: 2011, genres: ["Science Fiction"] },
  { title: "Zero to One", author: "<PERSON>", year: 2014, genres: ["Business", "Startup"] },
  { title: "The Hobbit", author: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", year: 1937, genres: ["Fantasy"] }
])

7. Insert log:
db.logs.insertOne({ message: "Database initialized", time: new Date() })

8. Update year of book "The Martian":
db.books.updateOne({ title: "The Martian" }, { $set: { year: 2022 } })

9. Find book with title "I, Robot":
db.books.find({ title: "I, Robot" })

10. Find books published between 1990 and 2010:
db.books.find({ year: { $gte: 1990, $lte: 2010 } })

11. Find books with genre "Science Fiction":
db.books.find({ genres: "Science Fiction" })

12. Skip first 2 books, limit next 3 sorted by year descending:
db.books.find().sort({ year: -1 }).skip(2).limit(3)

13. Find books where year is stored as integer:
db.books.find({ year: { $type: "int" } })

14. Find books without genres "Horror" or "Science Fiction":
db.books.find({ genres: { $nin: ["Horror", "Science Fiction"] } })

15. Delete books published before 2000:
db.books.deleteMany({ year: { $lt: 2000 } })

16. Aggregation: books after 2000 sorted by year descending:
db.books.aggregate([
  { $match: { year: { $gt: 2000 } } },
  { $sort: { year: -1 } }
])

17. Aggregation: books after 2000 showing title, author, year:
db.books.aggregate([
  { $match: { year: { $gt: 2000 } } },
  { $project: { title: 1, author: 1, year: 1, _id: 0 } }
])

18. Aggregation: unwind genres array:
db.books.aggregate([
  { $unwind: "$genres" }
])

19. Aggregation: join books with logs:
db.books.aggregate([
  {
    $lookup: {
      from: "logs",
      localField: "title",
      foreignField: "message",
      as: "book_logs"
    }
  }
])
